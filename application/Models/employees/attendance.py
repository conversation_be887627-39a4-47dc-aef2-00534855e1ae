from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date, timedelta
from sqlalchemy import cast, func

class Attendance(db.Model):
    """Model representing employee attendance."""
    __tablename__ = 'attendance'

    attendance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True),db.ForeignKey('employees.employee_id'), nullable=False)  # Reference to employee
    date = db.Column(db.Date, nullable=False)
    check_in_time = db.Column(db.DateTime, nullable=True)
    check_out_time = db.Column(db.DateTime, nullable=True)
    total_hours = db.Column(db.Float, nullable=True)
    status = db.Column(db.String(50), default='present', nullable=False)  # present, absent, late, half-day
    source = db.Column(db.String(50), nullable=False)  # biometric, manual, facial_recognition
    source_record_id = db.Column(db.String(255), nullable=True)  # ID from the source system (e.g., Records table ID)
    notes = db.Column(db.Text, nullable=True)

    # Shift-related fields
    shift_id = db.Column(UUID(as_uuid=True), db.ForeignKey('shifts.shift_id'), nullable=True)  # Foreign key reference to the shift table
    expected_start_time = db.Column(db.Time, nullable=True)  # Expected start time based on shift
    expected_end_time = db.Column(db.Time, nullable=True)  # Expected end time based on shift
    is_overtime = db.Column(db.Boolean, default=False, nullable=True)  # Whether this attendance includes overtime
    overtime_hours = db.Column(db.Float, nullable=True)  # Hours of overtime

    # Relationship with Shift model
    shift = db.relationship('Shift', backref=db.backref('attendances', lazy='dynamic'))

    # Relationship with Employee model
    employee = db.relationship('Employee', backref=db.backref('attendances', lazy='dynamic'))

    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID who created this record (for manual entries)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"Attendance [attendance_id={self.attendance_id}, employee_id={self.employee_id}, date={self.date}, status={self.status}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "attendance_id": str(self.attendance_id),
            "employee_id": str(self.employee_id),
            "date": self.date.strftime('%Y-%m-%d') if self.date else None,
            "check_in_time": self.check_in_time.strftime('%Y-%m-%d %H:%M:%S') if self.check_in_time else None,
            "check_out_time": self.check_out_time.strftime('%Y-%m-%d %H:%M:%S') if self.check_out_time else None,
            "total_hours": self.total_hours,
            "status": self.status,
            "source": self.source,
            "source_record_id": self.source_record_id,
            "notes": self.notes,
            "shift_id": str(self.shift_id) if self.shift_id else None,
            "expected_start_time": self.expected_start_time.strftime('%H:%M') if self.expected_start_time else None,
            "expected_end_time": self.expected_end_time.strftime('%H:%M') if self.expected_end_time else None,
            "is_overtime": self.is_overtime,
            "overtime_hours": self.overtime_hours,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_attendance_by_id(cls, session, attendance_id):
        """Get attendance record by ID."""
        try:
            # Convert string to UUID if needed
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return None

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            return attendance
        except Exception as e:
            app.logger.error(f"Error getting attendance by ID: {e}")
            return None

    @classmethod
    def get_attendance_by_employee_id(cls, session, employee_id, start_date=None, end_date=None):
        """Get attendance records for an employee within a date range."""
        try:
            # Convert string to UUID if needed
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return []

            query = session.query(cls).filter_by(employee_id=employee_id)

            if start_date:
                query = query.filter(cls.date >= start_date)

            if end_date:
                query = query.filter(cls.date <= end_date)

            return query.order_by(cls.date.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting attendance by employee ID: {e}")
            return []

    @classmethod
    def get_attendance_by_date(cls, session, date_value):
        """Get all attendance records for a specific date."""
        try:
            attendance_records = session.query(cls).filter_by(date=date_value).all()
            return attendance_records
        except Exception as e:
            app.logger.error(f"Error getting attendance by date: {e}")
            return []

    @classmethod
    def create_attendance(cls, session, **kwargs):
        """Create a new attendance record."""
        try:
            # Convert employee_id to UUID if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {kwargs['employee_id']}")
                    return None

            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            attendance = cls(**kwargs)
            session.add(attendance)
            session.commit()
            app.logger.info(f"Created new attendance record for employee ID: {attendance.employee_id}")
            return attendance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating attendance record: {e}")
            return None

    @classmethod
    def update_attendance(cls, session, attendance_id, **kwargs):
        """Update an attendance record."""
        try:
            # Convert attendance_id to UUID if it's a string
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return None

            # Convert employee_id to UUID if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {kwargs['employee_id']}")
                    return None

            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            if not attendance:
                app.logger.warning(f"Attendance record with ID {attendance_id} not found")
                return None

            # Update attendance attributes
            for key, value in kwargs.items():
                if hasattr(attendance, key):
                    setattr(attendance, key, value)

            # Recalculate total hours if check-in and check-out times are provided
            if attendance.check_in_time and attendance.check_out_time:
                time_diff = attendance.check_out_time - attendance.check_in_time
                attendance.total_hours = time_diff.total_seconds() / 3600  # Convert to hours

            session.commit()
            app.logger.info(f"Updated attendance record with ID: {attendance_id}")
            return attendance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating attendance record: {e}")
            return None

    @classmethod
    def delete_attendance(cls, session, attendance_id):
        """Delete an attendance record."""
        try:
            # Convert attendance_id to UUID if it's a string
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return False

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            if not attendance:
                app.logger.warning(f"Attendance record with ID {attendance_id} not found")
                return False

            session.delete(attendance)
            session.commit()
            app.logger.info(f"Deleted attendance record with ID: {attendance_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting attendance record: {e}")
            return False

    @classmethod
    def process_attendance_from_record(cls, session, record):
        """Process a biometric record and update attendance accordingly.

        Args:
            session: Database session
            record: Dictionary containing record data (enroll_id, records_time, etc.)

        Returns:
            dict: Result of the operation with status and message
        """
        try:
            # Import here to avoid circular imports
            from application.Models.Person import Person
            from application.Models.employees.employee import Employee

            # Extract data from record
            enroll_id = record.get('enroll_id')
            record_time_str = record.get('records_time')
            device_sn = record.get('device_serial_num')
            record_id = record.get('id', None)

            # Validate required data
            if not enroll_id or not record_time_str:
                app.logger.error(f"Missing required data in record: {record}")
                return {"success": False, "message": "Missing required data in record"}

            # Convert record time to datetime if it's a string
            if isinstance(record_time_str, str):
                try:
                    record_time = datetime.strptime(record_time_str, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    app.logger.error(f"Invalid record time format: {record_time_str}")
                    return {"success": False, "message": "Invalid record time format"}
            else:
                record_time = record_time_str

            # Get the record date (without time)
            record_date = record_time.date()

            # Find the person associated with this enroll_id
            app.logger.info(f"Looking up person with enroll_id: {enroll_id}")
            person = Person.get_person_given_id(session, enroll_id)

            if not person:
                app.logger.warning(f"No person found for enroll_id {enroll_id}")
                return {"success": False, "message": f"No person found for enroll_id {enroll_id}"}

            app.logger.info(f"Found person: {person.name} (ID: {person.id})")

            if not person.employee_id:
                app.logger.warning(f"Person with enroll_id {enroll_id} has no employee_id")
                return {"success": False, "message": f"Person with enroll_id {enroll_id} has no employee_id"}

            employee_id = person.employee_id
            app.logger.info(f"Found employee_id: {employee_id}")

            # Check if this employee exists in the Employee table
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                app.logger.warning(f"Employee with ID {employee_id} not found in Employee table")
                return {"success": False, "message": f"Employee with ID {employee_id} not found"}

            # Define time window to prevent multiple check-ins/check-outs (default: 30 minutes)
            time_window_minutes = 30

            # Check for any recent records within the time window to prevent false attendance
            try:
                # Use a simpler approach to check for recent records
                recent_time_threshold = record_time - timedelta(minutes=time_window_minutes)
                recent_records = session.query(cls).filter(
                    cls.employee_id == employee_id,
                    cls.date == record_date,
                    cls.check_in_time > recent_time_threshold
                ).all()

                if recent_records:
                    app.logger.info(f"Ignoring record within {time_window_minutes} minute window for employee {employee_id}")
                    return {"success": True, "message": "Record ignored (within time window)"}
            except Exception as e:
                app.logger.error(f"Error checking for recent records: {e}")
                # Continue processing even if this check fails

            # Check if there's an open attendance record (has check-in but no check-out)
            # Remove date restriction to allow for cross-day shifts and overnight work
            try:
                app.logger.info(f"Checking for open attendance record for employee {employee_id} (any date)")
                open_attendance = session.query(cls).filter(
                    cls.employee_id == employee_id,
                    cls.check_in_time.isnot(None),
                    cls.check_out_time.is_(None)
                ).order_by(cls.check_in_time.desc()).first()

                if open_attendance:
                    # Check if the open attendance is reasonable (not too old)
                    hours_since_checkin = (record_time - open_attendance.check_in_time).total_seconds() / 3600

                    # Allow up to 48 hours for very long shifts, but log if it's unusually long
                    if hours_since_checkin > 48:
                        app.logger.warning(f"Found very old open attendance record ({hours_since_checkin:.1f} hours old) for employee {employee_id}. Treating as new check-in instead.")
                        open_attendance = None
                    else:
                        app.logger.info(f"Found open attendance record: {open_attendance.attendance_id} ({hours_since_checkin:.1f} hours ago)")
                        # This is a check-out for an existing open record
                        open_attendance.check_out_time = record_time
                        open_attendance.source_record_id = str(record_id) if record_id else open_attendance.source_record_id

                    # Calculate total hours for this session
                    time_diff = open_attendance.check_out_time - open_attendance.check_in_time
                    total_hours = time_diff.total_seconds() / 3600  # Convert to hours
                    open_attendance.total_hours = total_hours

                    # Check for overtime if shift is assigned
                    if open_attendance.shift_id and open_attendance.expected_end_time:
                        from application.Models.employees.shift import Shift

                        # Get the shift
                        shift = Shift.get_shift_by_id(session, open_attendance.shift_id)

                        if shift:
                            # Calculate expected end time as datetime
                            expected_end_datetime = datetime.combine(open_attendance.date, open_attendance.expected_end_time)

                            # Add grace period for early departure
                            grace_minutes = shift.grace_period_early
                            earliest_departure = expected_end_datetime - timedelta(minutes=grace_minutes)

                            # Check if checkout is after expected end time (overtime)
                            if open_attendance.check_out_time > expected_end_datetime:
                                overtime_diff = open_attendance.check_out_time - expected_end_datetime
                                overtime_hours = overtime_diff.total_seconds() / 3600

                                # Only count as overtime if it's significant (e.g., more than 15 minutes)
                                if overtime_hours > 0.25:  # 15 minutes = 0.25 hours
                                    open_attendance.is_overtime = True
                                    open_attendance.overtime_hours = overtime_hours
                                    app.logger.info(f"Overtime detected: {overtime_hours:.2f} hours")

                            # Check if checkout is before expected end time (early departure)
                            elif open_attendance.check_out_time < earliest_departure:
                                # Employee left early, beyond grace period
                                app.logger.info(f"Early departure detected. Left at {open_attendance.check_out_time.time()}, expected after {earliest_departure.time()}")
                                # You might want to update status or add a note here

                    try:
                        session.commit()
                        app.logger.info(f"Updated attendance record with check-out at {record_time} for employee {employee_id}")
                        return {
                            "success": True,
                            "message": "Updated attendance with check-out time",
                            "attendance": open_attendance.to_dict()
                        }
                    except Exception as e:
                        session.rollback()
                        app.logger.error(f"Error updating attendance record: {e}")
                        return {"success": False, "message": f"Error updating attendance: {str(e)}"}

                # If no open attendance found (or it was too old), create new check-in
                if not open_attendance:
                    app.logger.info(f"No valid open attendance record found, creating new check-in")
                    # This is a new check-in (either first of the day or after a completed check-in/check-out cycle)
                    # Check if employee has an assigned shift for this day
                    from application.Models.employees.employee_shift import EmployeeShift
                    from application.Models.employees.shift import Shift

                    # Get current day of week (1-7, where 1 is Monday)
                    day_of_week = record_date.isoweekday()

                    # Find active shift assignment for this employee
                    shift_assignments = EmployeeShift.get_employee_shifts(
                        session,
                        employee_id,
                        active_only=True,
                        current_date=record_date
                    )

                    shift_id = None
                    expected_start_time = None
                    expected_end_time = None
                    status = 'present'  # Default status

                    if shift_assignments:
                        # Use the first active shift assignment
                        shift_assignment = shift_assignments[0]
                        shift = Shift.get_shift_by_id(session, shift_assignment.shift_id)

                        if shift:
                            # Check if this day is a working day for this shift
                            working_days = shift_assignment.custom_working_days or shift.working_days
                            working_days = [int(d) for d in working_days.split(',') if d]

                            if day_of_week in working_days:
                                shift_id = shift.shift_id

                                # Use custom times if set, otherwise use shift default times
                                start_time = shift_assignment.custom_start_time or shift.start_time
                                end_time = shift_assignment.custom_end_time or shift.end_time

                                # Set expected times
                                expected_start_time = start_time
                                expected_end_time = end_time

                                # Determine status based on arrival time
                                if start_time:
                                    # Convert datetime to time for comparison
                                    record_time_only = record_time.time()

                                    # Calculate late threshold with grace period
                                    grace_minutes = shift.grace_period_late
                                    late_threshold = datetime.combine(record_date, start_time) + timedelta(minutes=grace_minutes)
                                    late_threshold_time = late_threshold.time()

                                    # Check if employee is late
                                    if record_time_only > late_threshold_time:
                                        status = 'late'
                                        app.logger.info(f"Employee {employee_id} is late. Arrived at {record_time_only}, expected before {late_threshold_time}")

                    attendance_data = {
                        'employee_id': employee_id,
                        'date': record_date,
                        'check_in_time': record_time,
                        'check_out_time': None,  # Will be updated with later records
                        'total_hours': None,     # Will be calculated when check-out is recorded
                        'status': status,        # Status based on shift rules
                        'source': 'biometric',   # Source is biometric
                        'source_record_id': str(record_id) if record_id else None,
                        'shift_id': shift_id,    # Reference to the shift
                        'expected_start_time': expected_start_time,
                        'expected_end_time': expected_end_time,
                        'notes': f"Check-in from device {device_sn}"
                    }

                    app.logger.info(f"Creating new attendance with data: {attendance_data}")
                    try:
                        new_attendance = cls.create_attendance(session, **attendance_data)
                        if new_attendance:
                            app.logger.info(f"Created new attendance record with ID: {new_attendance.attendance_id}")
                            return {
                                "success": True,
                                "message": "Created new attendance record with check-in",
                                "attendance": new_attendance.to_dict()
                            }
                        else:
                            app.logger.error("create_attendance returned None")
                            return {"success": False, "message": "Failed to create attendance record"}
                    except Exception as e:
                        app.logger.error(f"Error creating attendance record: {e}")
                        import traceback
                        app.logger.error(traceback.format_exc())
                        return {"success": False, "message": f"Error creating attendance: {str(e)}"}
            except Exception as e:
                app.logger.error(f"Error in attendance processing: {e}")
                import traceback
                app.logger.error(traceback.format_exc())
                return {"success": False, "message": f"Error in attendance processing: {str(e)}"}

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error processing attendance from record: {e}")
            import traceback
            app.logger.error(traceback.format_exc())
            return {"success": False, "message": f"Error processing attendance: {str(e)}"}
